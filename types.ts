
export type View = 'chat' | 'dashboard';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  isBlocked?: boolean;
}

export interface FirewallRule {
  id: string;
  pattern: string;
  type: 'keyword' | 'regex';
  description: string;
}

export interface BlockedMessageLog {
  id: string;
  timestamp: string;
  message: string;
  reason: string;
  layer: string;
}

export type LlmProvider = 'google' | 'groq';

export interface LlmModel {
  id: string;
  name: string;
  provider: LlmProvider;
}