import type { FirewallRule, LlmModel } from './types';

// WARNING: Storing API keys in client-side code is insecure.
// This should be replaced with a backend proxy and environment variables in a production environment.
export const GROQ_API_KEY = '********************************************************';

export const AVAILABLE_MODELS: LlmModel[] = [
  { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', provider: 'google' },
  { id: 'llama-3.3-70b-versatile', name: 'LLaMA 3.3 70b', provider: 'groq' },
  { id: 'qwen/qwen3-32b', name: 'Qwen 3 32b', provider: 'groq' },
  { id: 'deepseek-r1-distill-llama-70b', name: 'DeepSeek R1 Distill 70b', provider: 'groq' },
  { id: 'meta-llama/llama-4-scout-17b-16e-instruct', name: 'LLaMA 4 Scout 17b', provider: 'groq' },
  { id: 'meta-llama/llama-guard-4-12b', name: 'LLaMA Guard 4 12b', provider: 'groq' },
  { id: 'moonshotai/kimi-k2-instruct', name: 'Kimi K2 Instruct', provider: 'groq' },
  { id: 'openai/gpt-oss-120b', name: 'GPT OSS 120b', provider: 'groq' },
];


export const INITIAL_RULES: FirewallRule[] = [
  { id: '1', pattern: 'api_key', type: 'keyword', description: 'Detects API keys' },
  { id: '2', pattern: 'password', type: 'keyword', description: 'Detects passwords' },
  { id: '3', pattern: 'secret', type: 'keyword', description: 'Detects secrets' },
  { id: '4', pattern: '\\b\\d{4}[ -]?\\d{4}[ -]?\\d{4}[ -]?\\d{4}\\b', type: 'regex', description: 'Detects Credit Card numbers' },
  { id: '5', pattern: '\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', type: 'regex', description: 'Detects email addresses' },
];

export const CHAT_SYSTEM_PROMPT: string = `You are HallucinationGuard, a secure and helpful AI assistant.
Your tone should be professional, courteous, and clear.

**Core Directives:**
1.  **Avoid Hallucinations:** You must not invent facts, figures, or any information. If you do not know the answer or lack sufficient data, you must explicitly state that you cannot provide the information. Do not provide speculative or guessed answers.
2.  **Adhere to Company Policy:** You must strictly follow security and data privacy policies. Never ask for, store, or repeat sensitive information such as Personally Identifiable Information (PII), secrets, passwords, API keys, or proprietary company data.
3.  **Politely Decline Inappropriate Requests:** If a user's prompt asks for sensitive information or violates company policy, you must politely decline the request, stating that you are not permitted to handle such data.
4.  **Be Helpful and Concise:** Provide accurate and relevant information within the scope of your knowledge and directives. Keep responses to the point.
`;