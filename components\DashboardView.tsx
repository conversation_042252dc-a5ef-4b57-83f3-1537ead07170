import React, { useState } from 'react';
import type { FirewallRule, BlockedMessageLog } from '../types';
import { AVAILABLE_MODELS } from '../constants';
import { PlusIcon } from './icons/PlusIcon';
import { TrashIcon } from './icons/TrashIcon';
import { EditIcon } from './icons/EditIcon';
import { DownloadIcon } from './icons/DownloadIcon';

interface DashboardViewProps {
  rules: FirewallRule[];
  setRules: React.Dispatch<React.SetStateAction<FirewallRule[]>>;
  blockedLogs: BlockedMessageLog[];
  clearBlockedLogs: () => void;
  selectedModelId: string;
  setSelectedModelId: (modelId: string) => void;
  enabledModelIds: string[];
  setEnabledModelIds: React.Dispatch<React.SetStateAction<string[]>>;
  fuzzyMatchThreshold: number;
  setFuzzyMatchThreshold: React.Dispatch<React.SetStateAction<number>>;
}

const DashboardView: React.FC<DashboardViewProps> = ({ rules, setRules, blockedLogs, clearBlockedLogs, selectedModelId, setSelectedModelId, enabledModelIds, setEnabledModelIds, fuzzyMatchThreshold, setFuzzyMatchThreshold }) => {
  const [newRulePattern, setNewRulePattern] = useState('');
  const [newRuleType, setNewRuleType] = useState<'keyword' | 'regex'>('keyword');
  const [newRuleDescription, setNewRuleDescription] = useState('');
  const [patternError, setPatternError] = useState<string | null>(null);

  // State for editing rules
  const [editingRuleId, setEditingRuleId] = useState<string | null>(null);
  const [editingRuleData, setEditingRuleData] = useState<{ pattern: string; type: 'keyword' | 'regex'; description: string; } | null>(null);
  const [editPatternError, setEditPatternError] = useState<string | null>(null);
  
  // State for deletion confirmation
  const [deletingRuleId, setDeletingRuleId] = useState<string | null>(null);
  const [showClearLogConfirm, setShowClearLogConfirm] = useState<boolean>(false);

  const handleAddRule = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newRulePattern.trim() || !newRuleDescription.trim()) return;

    if (newRuleType === 'regex') {
      try {
        new RegExp(newRulePattern);
      } catch (error) {
        setPatternError('Invalid regular expression pattern.');
        return;
      }
    }
    
    setPatternError(null);

    const newRule: FirewallRule = {
      id: Date.now().toString(),
      pattern: newRulePattern,
      type: newRuleType,
      description: newRuleDescription,
    };
    setRules((prev) => [...prev, newRule]);
    setNewRulePattern('');
    setNewRuleDescription('');
  };
  
  const handleDeleteRule = (id: string) => {
    setRules((prev) => prev.filter(rule => rule.id !== id));
  }

  const handleConfirmDelete = () => {
    if (deletingRuleId) {
      handleDeleteRule(deletingRuleId);
      setDeletingRuleId(null);
    }
  };

  const handleStartEdit = (rule: FirewallRule) => {
    setEditingRuleId(rule.id);
    setEditingRuleData({
        pattern: rule.pattern,
        type: rule.type,
        description: rule.description,
    });
    setEditPatternError(null);
  };

  const handleCancelEdit = () => {
    setEditingRuleId(null);
    setEditingRuleData(null);
    setEditPatternError(null);
  };

  const handleUpdateRule = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingRuleId || !editingRuleData) return;

    if (!editingRuleData.pattern.trim() || !editingRuleData.description.trim()) return;

    if (editingRuleData.type === 'regex') {
        try {
            new RegExp(editingRuleData.pattern);
        } catch (error) {
            setEditPatternError('Invalid regular expression pattern.');
            return;
        }
    }
    setEditPatternError(null);

    setRules(prevRules =>
        prevRules.map(rule =>
            rule.id === editingRuleId ? { ...rule, ...editingRuleData } : rule
        )
    );
    handleCancelEdit();
  };

  const handleToggleModel = (modelId: string) => {
    setEnabledModelIds(prevEnabledIds => {
      const isCurrentlyEnabled = prevEnabledIds.includes(modelId);
      if (isCurrentlyEnabled) {
        if (prevEnabledIds.length === 1) {
          alert("You must have at least one model enabled.");
          return prevEnabledIds;
        }
        return prevEnabledIds.filter(id => id !== modelId);
      } else {
        return [...prevEnabledIds, modelId];
      }
    });
  };

  const handleConfirmClearLogs = () => {
    clearBlockedLogs();
    setShowClearLogConfirm(false);
  };

  const handleExportCsv = () => {
    if (blockedLogs.length === 0) return;

    const escapeCsvField = (field: string) => `"${field.replace(/"/g, '""')}"`;

    const headers = ['id', 'timestamp', 'message', 'reason', 'layer'];
    const csvRows = [
      headers.join(','),
      ...blockedLogs.map(log => [
        escapeCsvField(log.id),
        escapeCsvField(log.timestamp),
        escapeCsvField(log.message),
        escapeCsvField(log.reason),
        escapeCsvField(log.layer),
      ].join(','))
    ];
    
    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'blocked-logs.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-8">
        <div>
            <h1 className="text-4xl font-bold tracking-tight">Manager Dashboard</h1>
            <p className="text-gray-600 mt-2">Configure firewall policies and monitor blocked messages.</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                    <h2 className="text-xl font-bold mb-4">Active Firewall Rules</h2>
                    <div className="space-y-3">
                        {rules.map((rule) => (
                            <div key={rule.id} className={`p-4 bg-gray-50 rounded-lg border border-gray-200 transition-all duration-300 border-l-4 ${rule.type === 'keyword' ? 'border-blue-500' : 'border-purple-500'}`}>
                                {editingRuleId === rule.id && editingRuleData ? (
                                    <form onSubmit={handleUpdateRule} className="space-y-4">
                                        <div>
                                            <label htmlFor={`edit-pattern-${rule.id}`} className="block text-sm font-medium text-gray-700">Pattern</label>
                                            <input id={`edit-pattern-${rule.id}`} type="text" value={editingRuleData.pattern} onChange={(e) => { setEditingRuleData({ ...editingRuleData, pattern: e.target.value }); if (editPatternError) setEditPatternError(null); }}
                                                className={`mt-1 block w-full px-3 py-2 bg-white border ${editPatternError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-900 focus:border-gray-900'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none sm:text-sm`}
                                            />
                                            {editPatternError && <p className="mt-2 text-sm text-red-600">{editPatternError}</p>}
                                        </div>
                                        <div>
                                            <label htmlFor={`edit-description-${rule.id}`} className="block text-sm font-medium text-gray-700">Description</label>
                                            <input id={`edit-description-${rule.id}`} type="text" value={editingRuleData.description} onChange={(e) => setEditingRuleData({ ...editingRuleData, description: e.target.value })}
                                                className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-900 focus:border-gray-900 sm:text-sm"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700">Rule Type</label>
                                            <div className="mt-1 flex rounded-md shadow-sm">
                                                <button type="button" onClick={() => { setEditingRuleData({ ...editingRuleData, type: 'keyword' }); setEditPatternError(null); }} className={`relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 text-sm font-medium ${editingRuleData.type === 'keyword' ? 'bg-gray-900 text-white z-10' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>Keyword</button>
                                                <button type="button" onClick={() => { setEditingRuleData({ ...editingRuleData, type: 'regex' }); }} className={`-ml-px relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 text-sm font-medium ${editingRuleData.type === 'regex' ? 'bg-gray-900 text-white z-10' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>Regex</button>
                                            </div>
                                        </div>
                                        <div className="flex items-center justify-end space-x-2 pt-2">
                                            <button type="button" onClick={handleCancelEdit} className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Cancel</button>
                                            <button type="submit" className="px-4 py-2 text-sm font-medium text-white bg-gray-900 border border-transparent rounded-md shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900">Save Changes</button>
                                        </div>
                                    </form>
                                ) : (
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <div className="flex items-center space-x-3">
                                                <span className={`px-2 py-0.5 text-xs font-semibold rounded-full ${rule.type === 'keyword' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}`}>{rule.type}</span>
                                                <p className="font-mono text-sm text-gray-800 bg-gray-200 px-2 py-1 rounded">{rule.pattern}</p>
                                            </div>
                                            <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
                                        </div>
                                        <div className="flex items-center space-x-1 flex-shrink-0">
                                            <button onClick={() => handleStartEdit(rule)} className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded-full transition" aria-label={`Edit rule ${rule.description}`}>
                                                <EditIcon className="h-5 w-5" />
                                            </button>
                                            <button onClick={() => setDeletingRuleId(rule.id)} className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-full transition" aria-label={`Delete rule ${rule.description}`}>
                                                <TrashIcon className="h-5 w-5" />
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                         {rules.length === 0 && (
                            <p className="text-center text-gray-500 py-4">No rules defined. Add a rule to get started.</p>
                        )}
                    </div>
                </div>
                <div className="bg-white p-6 rounded-xl border border-gray-200">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-bold">Blocked Message Log</h2>
                        {blockedLogs.length > 0 && (
                           <div className="flex items-center space-x-2">
                               <button
                                    onClick={handleExportCsv}
                                    className="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-200 rounded-md shadow-sm hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition"
                                    aria-label="Export blocked message logs as CSV"
                               >
                                    <DownloadIcon className="h-4 w-4 mr-1.5"/>
                                    Export CSV
                               </button>
                               <button 
                                    onClick={() => setShowClearLogConfirm(true)}
                                    className="flex items-center px-3 py-1.5 text-sm font-medium text-red-600 bg-red-100 border border-red-200 rounded-md shadow-sm hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition"
                                    aria-label="Clear all blocked message logs"
                               >
                                    <TrashIcon className="h-4 w-4 mr-1.5"/>
                                    Clear Log
                               </button>
                           </div>
                        )}
                    </div>
                    <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
                        {blockedLogs.length > 0 ? blockedLogs.map((log) => (
                            <div key={log.id} className="p-4 bg-red-50 rounded-lg border border-red-200">
                                <div className="flex items-start justify-between gap-4">
                                    <span className="text-xs font-semibold bg-red-200 text-red-800 px-2 py-1 rounded-full whitespace-nowrap">{log.layer}</span>
                                    <span className="text-xs text-gray-500 text-right">{new Date(log.timestamp).toLocaleString()}</span>
                                </div>
                                <p className="font-mono text-sm text-gray-700 bg-gray-100 p-2 rounded mt-2 break-words">"{log.message}"</p>
                                <p className="text-sm text-red-700 mt-2">Reason: {log.reason}</p>
                            </div>
                        )) : (
                            <p className="text-center text-gray-500 py-4">No messages have been blocked yet.</p>
                        )}
                    </div>
                </div>
            </div>

            <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-6">
                    <div className="bg-white p-6 rounded-xl border border-gray-200">
                      <h2 className="text-xl font-bold mb-4">Model Configuration</h2>
                       <div className="space-y-2">
                         {AVAILABLE_MODELS.map(model => {
                            const isEnabled = enabledModelIds.includes(model.id);
                            const isActive = isEnabled && selectedModelId === model.id;
                            return (
                                <div
                                    key={model.id}
                                    className={`
                                        flex items-center justify-between p-3 rounded-lg border transition-all duration-200
                                        ${isActive ? 'bg-gray-100 border-gray-900 shadow-sm' : 'border-gray-200'}
                                        ${isEnabled ? 'cursor-pointer hover:bg-gray-100' : 'bg-gray-50 opacity-60'}
                                    `}
                                    onClick={() => isEnabled && setSelectedModelId(model.id)}
                                >
                                    <div>
                                        <p className={`font-medium text-sm ${isEnabled ? 'text-gray-800' : 'text-gray-500'}`}>
                                            {model.name}
                                            {isActive && <span className="ml-2 text-xs font-semibold bg-gray-900 text-white px-2 py-0.5 rounded-full">Active</span>}
                                        </p>
                                        <p className={`text-xs capitalize ${isEnabled ? 'text-gray-500' : 'text-gray-400'}`}>{model.provider}</p>
                                    </div>
                                    <div onClick={(e) => e.stopPropagation()}>
                                        <label htmlFor={`model-toggle-${model.id}`} className="relative inline-flex items-center cursor-pointer">
                                          <input
                                            type="checkbox"
                                            id={`model-toggle-${model.id}`}
                                            className="sr-only peer"
                                            checked={isEnabled}
                                            onChange={() => handleToggleModel(model.id)}
                                          />
                                          <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-2 peer-focus:ring-gray-400 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-900"></div>
                                        </label>
                                    </div>
                                </div>
                            );
                        })}
                      </div>
                      {enabledModelIds.length === 0 && <p className="text-xs text-red-600 mt-2">Warning: No models enabled. Please enable at least one model.</p>}
                    </div>

                    <div className="bg-white p-6 rounded-xl border border-gray-200">
                        <h2 className="text-xl font-bold mb-4">Guardrail Settings</h2>
                        <div>
                            <label htmlFor="fuzzy-threshold" className="block text-sm font-medium text-gray-700">Fuzzy Matching Threshold</label>
                            <input
                                id="fuzzy-threshold"
                                type="number"
                                value={fuzzyMatchThreshold}
                                onChange={(e) => setFuzzyMatchThreshold(Math.max(0, parseInt(e.target.value, 10) || 0))}
                                min="0"
                                step="1"
                                className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-900 focus:border-gray-900 sm:text-sm"
                            />
                            <p className="mt-2 text-xs text-gray-500">
                                Levenshtein distance for keyword detection. Lower is stricter. '1' is recommended.
                            </p>
                        </div>
                    </div>

                    <form onSubmit={handleAddRule} className="bg-white p-6 rounded-xl border border-gray-200">
                        <h2 className="text-xl font-bold mb-4">Add New Rule</h2>
                        <div className="space-y-4">
                            <div>
                                <label htmlFor="pattern" className="block text-sm font-medium text-gray-700">Pattern</label>
                                <input
                                    id="pattern"
                                    type="text"
                                    value={newRulePattern}
                                    onChange={(e) => {
                                      setNewRulePattern(e.target.value);
                                      if (patternError) setPatternError(null);
                                    }}
                                    placeholder="e.g., credit_card_number"
                                    className={`mt-1 block w-full px-3 py-2 bg-white border ${patternError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-gray-900 focus:border-gray-900'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none sm:text-sm`}
                                    aria-invalid={!!patternError}
                                    aria-describedby={patternError ? 'pattern-error' : undefined}
                                />
                                {patternError && (
                                    <p id="pattern-error" className="mt-2 text-sm text-red-600">{patternError}</p>
                                )}
                            </div>
                            <div>
                                <label htmlFor="description" className="block text-sm font-medium text-gray-700">Description</label>
                                <input
                                    id="description"
                                    type="text"
                                    value={newRuleDescription}
                                    onChange={(e) => setNewRuleDescription(e.target.value)}
                                    placeholder="e.g., Detects credit card numbers"
                                    className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-900 focus:border-gray-900 sm:text-sm"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Rule Type</label>
                                <div className="mt-1 flex rounded-md shadow-sm">
                                    <button type="button" onClick={() => { setNewRuleType('keyword'); setPatternError(null); }} className={`relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 text-sm font-medium ${newRuleType === 'keyword' ? 'bg-gray-900 text-white z-10' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>
                                        Keyword
                                    </button>
                                    <button type="button" onClick={() => { setNewRuleType('regex'); setPatternError(null); }} className={`-ml-px relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 text-sm font-medium ${newRuleType === 'regex' ? 'bg-gray-900 text-white z-10' : 'bg-white text-gray-700 hover:bg-gray-50'}`}>
                                        Regex
                                    </button>
                                </div>
                            </div>
                            <button type="submit" className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-900 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900">
                               <PlusIcon className="h-5 w-5 mr-2"/> Add Rule
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {deletingRuleId && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" aria-modal="true" role="dialog">
                <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md mx-4 transform transition-all">
                    <h3 className="text-lg font-bold text-gray-900">Confirm Deletion</h3>
                    <p className="mt-2 text-sm text-gray-600">
                        Are you sure you want to delete this firewall rule? This action cannot be undone.
                    </p>
                    <div className="mt-6 flex justify-end space-x-3">
                        <button
                            type="button"
                            onClick={() => setDeletingRuleId(null)}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={handleConfirmDelete}
                            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        )}

        {showClearLogConfirm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" aria-modal="true" role="dialog">
                <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md mx-4 transform transition-all">
                    <h3 className="text-lg font-bold text-gray-900">Confirm Clear Log</h3>
                    <p className="mt-2 text-sm text-gray-600">
                        Are you sure you want to clear all blocked message logs? This action cannot be undone.
                    </p>
                    <div className="mt-6 flex justify-end space-x-3">
                        <button
                            type="button"
                            onClick={() => setShowClearLogConfirm(false)}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="button"
                            onClick={handleConfirmClearLogs}
                            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                            Clear Log
                        </button>
                    </div>
                </div>
            </div>
        )}
    </div>
  );
};

export default DashboardView;