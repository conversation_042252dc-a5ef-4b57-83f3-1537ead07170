import { GoogleGenAI } from "@google/genai";
import Groq from 'groq-sdk';
import { AVAILABLE_MODELS, CHAT_SYSTEM_PROMPT, GROQ_API_KEY } from '../constants';

// Gemini Client
const googleAi = new GoogleGenAI({ apiKey: process.env.API_KEY });

// Groq Client
const groq = new Groq({
  apiKey: GROQ_API_KEY,
  dangerouslyAllowBrowser: true, // Required for client-side usage
});

/**
 * Gets a response from a selected LLM.
 * @param prompt The user's prompt.
 * @param modelId The ID of the model to use (e.g., 'gemini-2.5-flash').
 * @returns A promise that resolves with the LLM's response string.
 */
export const getLlmResponse = async (prompt: string, modelId: string): Promise<string> => {
  const model = AVAILABLE_MODELS.find(m => m.id === modelId);

  if (!model) {
    throw new Error(`Model with id "${modelId}" not found.`);
  }

  try {
    if (model.provider === 'google') {
      console.log(`Sending prompt to Gemini (${model.id}):`, prompt);
      const response = await googleAi.models.generateContent({
          model: model.id,
          contents: prompt,
          config: {
            systemInstruction: CHAT_SYSTEM_PROMPT,
          }
      });
      return response.text;

    } else if (model.provider === 'groq') {
      console.log(`Sending prompt to Groq (${model.id}):`, prompt);
      const chatCompletion = await groq.chat.completions.create({
          messages: [
              {
                  role: "system",
                  content: CHAT_SYSTEM_PROMPT,
              },
              {
                  role: "user",
                  content: prompt,
              },
          ],
          model: model.id,
      });
      return chatCompletion.choices[0]?.message?.content || "Sorry, I couldn't get a response.";
    } else {
        throw new Error(`Unsupported model provider: ${model.provider}`);
    }

  } catch (error) {
    console.error(`Error fetching from ${model.provider} API:`, error);
    throw new Error(`Failed to get response from ${model.name}.`);
  }
};