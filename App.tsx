
import React, { useState, useEffect } from 'react';
import type { View, FirewallRule, BlockedMessageLog } from './types';
import { INITIAL_RULES, AVAILABLE_MODELS } from './constants';
import Header from './components/Header';
import ChatView from './components/ChatView';
import DashboardView from './components/DashboardView';

const App: React.FC = () => {
  const [view, setView] = useState<View>('chat');
  const [rules, setRules] = useState<FirewallRule[]>(INITIAL_RULES);
  const [blockedLogs, setBlockedLogs] = useState<BlockedMessageLog[]>([]);
  const [selectedModelId, setSelectedModelId] = useState<string>(AVAILABLE_MODELS[0].id);
  const [enabledModelIds, setEnabledModelIds] = useState<string[]>(() => AVAILABLE_MODELS.map(m => m.id));
  const [fuzzyMatchThreshold, setFuzzyMatchThreshold] = useState<number>(1);

  useEffect(() => {
    // If the currently selected model is no longer enabled,
    // default to the first available enabled model.
    if (!enabledModelIds.includes(selectedModelId) && enabledModelIds.length > 0) {
      setSelectedModelId(enabledModelIds[0]);
    }
  }, [enabledModelIds, selectedModelId]);

  const addBlockedLog = (log: Omit<BlockedMessageLog, 'id' | 'timestamp'>) => {
    const newLog: BlockedMessageLog = {
      ...log,
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
    };
    setBlockedLogs(prev => [newLog, ...prev]);
  };

  const clearBlockedLogs = () => {
    setBlockedLogs([]);
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] text-gray-900">
      <Header currentView={view} setCurrentView={setView} />
      <main className="p-4 sm:p-6 md:p-8 max-w-7xl mx-auto">
        {view === 'chat' && <ChatView rules={rules} addBlockedLog={addBlockedLog} selectedModelId={selectedModelId} fuzzyMatchThreshold={fuzzyMatchThreshold} />}
        {view === 'dashboard' && <DashboardView rules={rules} setRules={setRules} blockedLogs={blockedLogs} clearBlockedLogs={clearBlockedLogs} selectedModelId={selectedModelId} setSelectedModelId={setSelectedModelId} enabledModelIds={enabledModelIds} setEnabledModelIds={setEnabledModelIds} fuzzyMatchThreshold={fuzzyMatchThreshold} setFuzzyMatchThreshold={setFuzzyMatchThreshold} />}
      </main>
    </div>
  );
};

export default App;
