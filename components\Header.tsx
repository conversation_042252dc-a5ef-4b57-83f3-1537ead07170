
import React from 'react';
import type { View } from '../types';
import { LogoIcon } from './icons/LogoIcon';

interface HeaderProps {
  currentView: View;
  setCurrentView: (view: View) => void;
}

const Header: React.FC<HeaderProps> = ({ currentView, setCurrentView }) => {
  const navItems: { id: View; label: string }[] = [
    { id: 'chat', label: 'Chat' },
    { id: 'dashboard', label: 'Manager Dashboard' },
  ];

  return (
    <header className="bg-white/80 backdrop-blur-lg sticky top-0 z-10 border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <LogoIcon className="h-8 w-8 text-gray-900" />
            <span className="text-xl font-bold tracking-tight">HallucinationGuard</span>
          </div>
          <nav className="hidden md:flex items-center space-x-2 bg-gray-100 p-1 rounded-lg">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setCurrentView(item.id)}
                className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-colors duration-200 ${
                  currentView === item.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:bg-gray-200'
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>
          <div className="flex items-center space-x-4">
            <button className="text-sm font-semibold text-gray-600 hover:text-gray-900">
              Log in
            </button>
            <button className="text-sm font-semibold text-white bg-gray-900 hover:bg-gray-800 px-4 py-2 rounded-lg">
              Sign up
            </button>
          </div>
        </div>
      </div>
       <div className="md:hidden p-2">
            <div className="flex items-center justify-center space-x-1 bg-gray-100 p-1 rounded-lg">
                {navItems.map((item) => (
                    <button
                        key={item.id}
                        onClick={() => setCurrentView(item.id)}
                        className={`w-full px-3 py-2 text-sm font-semibold rounded-md transition-colors duration-200 ${
                            currentView === item.id
                                ? 'bg-white text-gray-900 shadow-sm'
                                : 'text-gray-600 hover:bg-gray-200'
                        }`}
                    >
                        {item.label}
                    </button>
                ))}
            </div>
        </div>
    </header>
  );
};

export default Header;
