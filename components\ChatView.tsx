
import React, { useState, useRef, useEffect } from 'react';
import type { ChatMessage, FirewallRule, BlockedMessageLog } from '../types';
import { checkMessageWithAllLayers } from '../services/guardrailService';
import { getLlmResponse } from '../services/llmService';
import { UserIcon } from './icons/UserIcon';
import { BotIcon } from './icons/BotIcon';
import { ShieldIcon } from './icons/ShieldIcon';

interface ChatViewProps {
  rules: FirewallRule[];
  addBlockedLog: (log: Omit<BlockedMessageLog, 'id' | 'timestamp'>) => void;
  selectedModelId: string;
  fuzzyMatchThreshold: number;
}

const ChatView: React.FC<ChatViewProps> = ({ rules, addBlockedLog, selectedModelId, fuzzyMatchThreshold }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'initial',
      role: 'assistant',
      content: "Hello! I'm your secure AI assistant. How can I help you today?",
    },
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [statusText, setStatusText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
    };
    
    const messageToCheck = input;
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    setStatusText('Scanning for sensitive data...');
    const { blocked, reason, layer } = await checkMessageWithAllLayers(messageToCheck, rules, fuzzyMatchThreshold);

    if (blocked && reason && layer) {
      const blockedMessage: ChatMessage = {
        id: Date.now().toString() + '-blocked',
        role: 'system',
        content: `Message blocked at ${layer} layer.\nReason: ${reason}.`,
        isBlocked: true,
      };
      setMessages((prev) => [...prev, blockedMessage]);
      addBlockedLog({ message: messageToCheck, reason, layer });
      setIsLoading(false);
      setStatusText('');
      return;
    }
    
    setStatusText('Thinking...');
    try {
        const assistantResponse = await getLlmResponse(messageToCheck, selectedModelId);
        const assistantMessage: ChatMessage = {
            id: Date.now().toString() + '-response',
            role: 'assistant',
            content: assistantResponse,
        };
        setMessages((prev) => [...prev, assistantMessage]);
    } catch (error) {
        console.error(error);
        const errorMessage: ChatMessage = {
            id: Date.now().toString() + '-error',
            role: 'system',
            content: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,
            isBlocked: true,
        };
        setMessages((prev) => [...prev, errorMessage]);
    } finally {
        setIsLoading(false);
        setStatusText('');
    }
  };

  return (
    <div className="flex flex-col h-[calc(100vh-10rem)] bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex items-start gap-4 ${msg.role === 'user' ? 'justify-end' : ''}`}>
             {msg.role === 'assistant' && <BotIcon className="w-8 h-8 flex-shrink-0 text-gray-500" />}
             {msg.role === 'system' && <ShieldIcon className="w-8 h-8 flex-shrink-0 text-red-500" />}
            
            <div className={`max-w-lg px-4 py-3 rounded-2xl ${
              msg.role === 'user' ? 'bg-gray-900 text-white rounded-br-none' : 
              msg.role === 'assistant' ? 'bg-gray-100 text-gray-800 rounded-bl-none' : 
              'bg-red-100 text-red-800 border border-red-200 rounded-bl-none'
            }`}>
              <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
            </div>

            {msg.role === 'user' && <UserIcon className="w-8 h-8 flex-shrink-0 text-gray-500" />}
          </div>
        ))}
        {isLoading && (
            <div className="flex items-start gap-4">
                <BotIcon className="w-8 h-8 flex-shrink-0 text-gray-500" />
                <div className="max-w-lg px-4 py-3 rounded-2xl bg-gray-100 text-gray-800 rounded-bl-none">
                     <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1.5">
                            <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                            <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                            <span className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"></span>
                        </div>
                        {statusText && <p className="text-sm text-gray-600">{statusText}</p>}
                    </div>
                </div>
            </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200 bg-white rounded-b-xl">
        <div className="relative">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask anything..."
            className="w-full pl-4 pr-12 py-3 bg-gray-100 border-transparent rounded-lg focus:ring-2 focus:ring-gray-900 focus:outline-none transition"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-gray-900 text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed transition"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatView;